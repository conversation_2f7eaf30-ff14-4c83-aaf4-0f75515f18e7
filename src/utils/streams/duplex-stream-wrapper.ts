import type { ClientDuplexStream } from '@grpc/grpc-js'
import type { Awaitable } from '@kdt310722/utils/promise'
import { StreamWrapper, type StreamWrapperOptions } from './stream-wrapper'

export interface DuplexStreamWrapperOptions extends StreamWrapperOptions {
}

export class DuplexStreamWrapper<TRequest = unknown, TResponse = unknown> extends StreamWrapper<TResponse> {
    public constructor(protected override readonly subscriber: () => Awaitable<ClientDuplexStream<TRequest, TResponse>>, options: DuplexStreamWrapperOptions = {}) {
        super(subscriber, options)
    }
}
