import type { ClientReadableStream } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tryCatch } from '@kdt310722/utils/function'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Awaitable, createDeferred, tap, withTimeout } from '@kdt310722/utils/promise'
import { IdleTimeoutError, StreamError } from '../../errors'
import { isIgnorableGrpcError } from '../grpc'
import { ResubscribeManager, type ResubscribeManagerEvents, type ResubscribeManagerOptions } from './resubscribe-manager'
import type { ClientReadableStreamWithClose, OnStreamChangeFn } from './types'

export interface StreamWrapperTimeoutOptions {
    subscribe?: number
    close?: number
    idle?: number
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
    resubscribe?: ResubscribeManagerOptions | boolean
    destroyOnError?: boolean
    destroyOnCloseFail?: boolean
}

export type StreamWrapperEvents<T = unknown> = ResubscribeManagerEvents & {
    subscribed: () => void
    closed: (isExplicitly: boolean) => void
    data: (data: T) => void
}

export class StreamWrapper<T = unknown> extends Emitter<StreamWrapperEvents<T>, true> {
    protected readonly resubscribeManager: ResubscribeManager<T>

    protected readonly subscribeTimeout: number
    protected readonly closeTimeout: number
    protected readonly idleTimeout: number

    protected readonly destroyOnError: boolean
    protected readonly destroyOnCloseFail: boolean

    protected stream?: Promise<ClientReadableStreamWithClose<T>>
    protected closePromise?: Promise<void>

    protected idleTimer?: NodeJS.Timeout
    protected lastDataTime?: number

    public constructor(protected readonly subscriber: () => Awaitable<ClientReadableStream<T>>, { timeout = {}, destroyOnError = true, destroyOnCloseFail = true, resubscribe = true }: StreamWrapperOptions = {}) {
        super()

        this.resubscribeManager = new ResubscribeManager<T>(resolveNestedOptions(resubscribe) || { enabled: false }, this)

        this.subscribeTimeout = timeout.subscribe ?? 30_000
        this.closeTimeout = timeout.close ?? this.subscribeTimeout
        this.idleTimeout = timeout.idle ?? 60_000
        this.destroyOnError = destroyOnError
        this.destroyOnCloseFail = destroyOnCloseFail
    }

    public async subscribe() {
        await this.closePromise
        await (this.stream ??= this.createStream((stream) => this.stream = notNullish(stream) ? Promise.resolve(stream) : undefined).then(tap(() => this.emit('subscribed'))))
    }

    public async close() {
        await this.stream?.then(async (stream) => this.closePromise ??= stream.close().finally(() => {
            this.stream = undefined
            this.closePromise = undefined
        }))
    }

    protected async createStream(onStreamChange?: OnStreamChangeFn<T>): Promise<ClientReadableStreamWithClose<T>> {
        const promise = createDeferred<void>()
        const stream = await this.subscriber()

        let metadataHandler: () => void
        let errorHandler: (error: Error) => void
        let closeHandler: () => void
        let endHandler: () => void
        let dataHandler: (data: T) => void

        const cleanup = () => {
            stream.removeListener('metadata', metadataHandler)
            stream.removeListener('error', errorHandler)
            stream.removeListener('close', closeHandler)
            stream.removeListener('end', endHandler)
            stream.removeListener('data', dataHandler)
        }

        let isExplicitlyClosed = false

        stream.on('metadata', metadataHandler = () => promise.isSettled || promise.resolve())
        stream.on('error', errorHandler = (error) => (promise.isSettled ? this.handleError(stream, error) : promise.reject(error)))
        stream.on('close', closeHandler = () => (promise.isSettled ? this.handleClose(stream, cleanup, isExplicitlyClosed, onStreamChange) : promise.reject(new StreamError('Stream closed unexpectedly').withStream(stream))))
        stream.on('end', endHandler = () => (promise.isSettled ? stream.destroy() : promise.reject(new StreamError('Stream ended unexpectedly').withStream(stream))))
        stream.on('data', dataHandler = (data: T) => (promise.isSettled ? this.handleData(stream, data) : promise.resolve()))

        await withTimeout(promise, this.subscribeTimeout, () => new StreamError('Subscribe timeout').withStream(stream)).catch((error) => {
            cleanup()
            tryCatch(() => stream.destroy(), null)

            throw error
        })

        this.runIdleTimer(stream)

        const close = async () => {
            return Promise.resolve(isExplicitlyClosed = true).then(() => this.closeStream(stream))
        }

        return Object.assign(stream, { close })
    }

    protected async closeStream(stream: ClientReadableStream<T>) {
        const promise = createDeferred<void>()

        let closeHandler: () => void
        let errorHandler: (error: Error) => void

        stream.once('close', closeHandler = () => promise.resolve())
        stream.once('error', errorHandler = (error) => promise.reject(error))
        stream.cancel()

        const handleError = (error: Error) => {
            if (this.destroyOnCloseFail) {
                stream.destroy(error)
            } else {
                throw error
            }
        }

        await withTimeout(promise, this.closeTimeout, () => new StreamError('Close timeout').withStream(stream)).catch(handleError).finally(() => {
            stream.removeListener('close', closeHandler)
            stream.removeListener('error', errorHandler)
        })
    }

    protected handleData(stream: ClientReadableStream<T>, data: T) {
        this.emit('data', data)
        this.lastDataTime = Date.now()
        this.runIdleTimer(stream)
    }

    protected runIdleTimer(stream: ClientReadableStream<T>) {
        if (this.idleTimer) {
            clearTimeout(this.idleTimer)
        }

        this.idleTimer = setTimeout(() => this.handleError(stream, new IdleTimeoutError().withStream(stream).withLastDataTime(this.lastDataTime)), this.idleTimeout)
    }

    protected handleError(stream: ClientReadableStream<T>, error: unknown) {
        if (isIgnorableGrpcError(error)) {
            return
        }

        this.emit('error', this.resubscribeManager.setLatestError(error))

        if (this.destroyOnError) {
            stream.destroy()
        }
    }

    protected handleClose(stream: ClientReadableStream<T>, cleanup: () => void, isExplicitly: boolean, onStreamChange?: OnStreamChangeFn<T>) {
        cleanup()

        this.emit('closed', isExplicitly)

        if (isExplicitly) {
            this.resubscribeManager.reset()
        } else {
            this.resubscribeManager.handleResubscribe(stream, onStreamChange)
        }

        clearTimeout(this.idleTimer)

        this.idleTimer = undefined
        this.lastDataTime = undefined
    }
}
